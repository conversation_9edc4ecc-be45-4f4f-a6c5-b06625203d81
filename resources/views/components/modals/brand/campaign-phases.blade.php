{{-- Brand Campaign Phases Modal Component --}}
<div class="modal fade" id="brandCampaignPhasesModal" tabindex="-1" aria-labelledby="brandCampaignPhasesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="brandCampaignPhasesModalLabel">Campaign Phases</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="brand-campaign-phases-container">
                    {{-- Request Phase --}}
                    <div class="phase-card request-phase">
                        <div class="phase-header">
                            <span>Request phase</span>
                            <div class="phase-duration">3 Days</div>
                        </div>
                        <div class="phase-content">
                            Customer requests influencer - Influencer have 3 Days to respond. If these three days are over, every request which was not answered will be automatically declined. Customer can still request additional influencer through "manage influencers". Customer is able to start the campaign (and pay for it) all the time, as long as at least one influencer has accepted the campaign. If he pays in the request phase, the payment phase will be skipped.
                        </div>
                    </div>

                    {{-- Payment Phase --}}
                    <div class="phase-card payment-phase">
                        <div class="phase-header">
                            <span>Payment phase</span>
                            <div class="phase-duration">3 Days</div>
                        </div>
                        <div class="phase-content">
                            After these three days, customer is only able to start the campaign while paying. Customer has three days for this. During this period, its not possible for the customer to add additional influencers. If the customer does not pay within this period, the campaign will be cancelled automatically. (So campaign gets cancelled 6 days after creating, if it was not paid & started by the customer)
                        </div>
                    </div>

                    {{-- Submit Phase --}}
                    <div class="phase-card submit-phase">
                        <div class="phase-header">
                            <span>Submit phase</span>
                            <div class="phase-duration">10 Days</div>
                        </div>
                        <div class="phase-content">
                            Every influencer has 7 days to submit their post. If influencer does not submit within 7 days, his campaign gets cancelled automatically. Customer can also review each submitted post from influencer, even though its not review-phase.
                        </div>
                    </div>

                    {{-- Review Phase --}}
                    <div class="phase-card review-phase">
                        <div class="phase-header">
                            <span>Review phase</span>
                            <div class="phase-duration">7 Days</div>
                        </div>
                        <div class="phase-content">
                            Now every influencer has either submitted, or cancelled. Customer has seven days to review each submit of the influencer. If the customer does not review a submit, then it will be confirmed automatically for the influencer (but without rating). If a complaint is ongoing, then it wont be confirmed automatically after these 7 days.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ensure modal is hidden by default */
    #brandCampaignPhasesModal {
        display: none !important;
    }

    #brandCampaignPhasesModal.show {
        display: block !important;
    }

    .brand-campaign-phases-container {
        display: flex;
        flex-direction: column;
        gap: 0;
        padding: 10px;
    }

    .brand-campaign-phases-container .phase-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }

    .brand-campaign-phases-container .phase-header {
        padding: 15px 20px;
        color: white;
        font-weight: 600;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .brand-campaign-phases-container .phase-duration {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
    }

    .brand-campaign-phases-container .phase-duration::before {
        content: "⏱";
        font-size: 16px;
    }

    .brand-campaign-phases-container .phase-content {
        padding: 20px;
        color: #333;
        font-size: 14px;
        line-height: 1.6;
        background-color: #f8f9fa;
    }

    .brand-campaign-phases-container .request-phase .phase-header {
        background: linear-gradient(135deg, #4FC3F7, #29B6F6);
    }

    .brand-campaign-phases-container .payment-phase .phase-header {
        background: linear-gradient(135deg, #FFD54F, #FFC107);
    }

    .brand-campaign-phases-container .submit-phase .phase-header {
        background: linear-gradient(135deg, #FF8A65, #FF7043);
    }

    .brand-campaign-phases-container .review-phase .phase-header {
        background: linear-gradient(135deg, #81C784, #66BB6A);
    }

    @media (max-width: 768px) {
        .brand-campaign-phases-container .phase-header {
            padding: 12px 15px;
            font-size: 14px;
        }
        
        .brand-campaign-phases-container .phase-content {
            padding: 15px;
            font-size: 13px;
        }
        
        .brand-campaign-phases-container .phase-duration {
            font-size: 12px;
        }
    }
</style>
